import { executeQuery, executeQuerySingle } from './database-config';
import { CartItem } from './session-manager';

export interface CartItemInput {
  product_id: string;
  product_title: string;
  product_title_ar?: string;
  product_image?: string;
  product_price: number;
  quantity: number;
}

export interface CartSummary {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
}

// إضافة منتج إلى عربة التسوق
export async function addToCart(sessionId: string, item: CartItemInput): Promise<CartItem> {
  try {
    // التحقق من وجود المنتج في العربة
    const existingItem = await executeQuerySingle<CartItem>(
      'SELECT * FROM cart_items WHERE session_id = ? AND product_id = ?',
      [sessionId, item.product_id]
    );

    if (existingItem) {
      // تحديث الكمية إذا كان المنتج موجود
      const newQuantity = existingItem.quantity + item.quantity;
      await executeQuery(
        'UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE id = ?',
        [newQuantity, existingItem.id]
      );

      return {
        ...existingItem,
        quantity: newQuantity,
        updated_at: new Date()
      };
    } else {
      // إضافة منتج جديد
      const result = await executeQuery(
        `INSERT INTO cart_items 
         (session_id, product_id, product_title, product_title_ar, product_image, product_price, quantity, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          sessionId,
          item.product_id,
          item.product_title,
          item.product_title_ar || item.product_title,
          item.product_image,
          item.product_price,
          item.quantity
        ]
      );

      // الحصول على المنتج المضاف
      const newItem = await executeQuerySingle<CartItem>(
        'SELECT * FROM cart_items WHERE id = ?',
        [result.insertId]
      );

      return newItem!;
    }
  } catch (error) {
    console.error('خطأ في إضافة المنتج إلى العربة:', error);
    throw new Error('فشل في إضافة المنتج إلى العربة');
  }
}

// الحصول على عربة التسوق
export async function getCart(sessionId: string): Promise<CartSummary> {
  try {
    const items = await executeQuery<CartItem>(
      'SELECT * FROM cart_items WHERE session_id = ? ORDER BY created_at DESC',
      [sessionId]
    );

    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = items.reduce((sum, item) => sum + (item.product_price * item.quantity), 0);

    return {
      items,
      totalItems,
      totalAmount
    };
  } catch (error) {
    console.error('خطأ في جلب عربة التسوق:', error);
    throw new Error('فشل في جلب عربة التسوق');
  }
}

// تحديث كمية منتج في العربة
export async function updateCartItemQuantity(sessionId: string, productId: string, quantity: number): Promise<boolean> {
  try {
    if (quantity <= 0) {
      return await removeFromCart(sessionId, productId);
    }

    const result = await executeQuery(
      'UPDATE cart_items SET quantity = ?, updated_at = NOW() WHERE session_id = ? AND product_id = ?',
      [quantity, sessionId, productId]
    );

    return result.affectedRows > 0;
  } catch (error) {
    console.error('خطأ في تحديث كمية المنتج:', error);
    throw new Error('فشل في تحديث كمية المنتج');
  }
}

// حذف منتج من العربة
export async function removeFromCart(sessionId: string, productId: string): Promise<boolean> {
  try {
    const result = await executeQuery(
      'DELETE FROM cart_items WHERE session_id = ? AND product_id = ?',
      [sessionId, productId]
    );

    return result.affectedRows > 0;
  } catch (error) {
    console.error('خطأ في حذف المنتج من العربة:', error);
    throw new Error('فشل في حذف المنتج من العربة');
  }
}

// تفريغ عربة التسوق
export async function clearCart(sessionId: string): Promise<boolean> {
  try {
    const result = await executeQuery(
      'DELETE FROM cart_items WHERE session_id = ?',
      [sessionId]
    );

    return result.affectedRows >= 0; // حتى لو كانت العربة فارغة
  } catch (error) {
    console.error('خطأ في تفريغ العربة:', error);
    throw new Error('فشل في تفريغ العربة');
  }
}

// الحصول على عدد المنتجات في العربة
export async function getCartItemCount(sessionId: string): Promise<number> {
  try {
    const result = await executeQuerySingle<{ total: number }>(
      'SELECT COALESCE(SUM(quantity), 0) as total FROM cart_items WHERE session_id = ?',
      [sessionId]
    );

    return result?.total || 0;
  } catch (error) {
    console.error('خطأ في جلب عدد المنتجات:', error);
    return 0;
  }
}

// التحقق من وجود منتج في العربة
export async function isProductInCart(sessionId: string, productId: string): Promise<boolean> {
  try {
    const item = await executeQuerySingle<CartItem>(
      'SELECT id FROM cart_items WHERE session_id = ? AND product_id = ?',
      [sessionId, productId]
    );

    return !!item;
  } catch (error) {
    console.error('خطأ في التحقق من وجود المنتج:', error);
    return false;
  }
}

// نقل عربة التسوق من localStorage إلى قاعدة البيانات (للترحيل)
export async function migrateCartFromLocalStorage(sessionId: string, localStorageItems: any[]): Promise<boolean> {
  try {
    // تفريغ العربة الحالية أولاً
    await clearCart(sessionId);

    // إضافة المنتجات من localStorage
    for (const item of localStorageItems) {
      await addToCart(sessionId, {
        product_id: item.id,
        product_title: item.title,
        product_title_ar: item.titleAr || item.title,
        product_image: item.image,
        product_price: parseFloat(item.price),
        quantity: parseInt(item.quantity)
      });
    }

    return true;
  } catch (error) {
    console.error('خطأ في ترحيل العربة:', error);
    return false;
  }
}
