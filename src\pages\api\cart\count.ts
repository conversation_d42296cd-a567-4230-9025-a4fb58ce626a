import { NextApiRequest, NextApiResponse } from 'next';
import { getOrCreateSession } from '../../../lib/session-manager';
import { getCartItemCount } from '../../../lib/cart-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'طريقة الطلب غير مدعومة',
      messageEn: 'Method not allowed'
    });
  }

  try {
    // الحصول على معرف الجلسة أو إنشاء جلسة جديدة
    const sessionId = await getOrCreateSession(req, res);

    // جلب عدد المنتجات في العربة
    const count = await getCartItemCount(sessionId);

    return res.status(200).json({
      success: true,
      data: {
        count
      }
    });

  } catch (error) {
    console.error('Cart count error:', error);
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي',
      messageEn: 'Internal server error',
      data: {
        count: 0
      }
    });
  }
}
