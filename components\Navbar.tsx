'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Locale, getOppositeLocale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';
import { useHeaderSettings, useSiteSettings } from '../src/hooks/useSiteSettings';
import { getCartItemCount } from '../src/lib/session-cart';
import CategoriesDropdown from './CategoriesDropdown';

interface NavbarProps {
  locale: Locale;
}

const Navbar: React.FC<NavbarProps> = ({ locale }) => {
  const [cartCount, setCartCount] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();

  const t = (key: string) => getTranslation(locale, key as any);
  const { headerSettings, loading } = useHeaderSettings();
  const { settings } = useSiteSettings();

  useEffect(() => {
    setIsMounted(true);
    const updateCartCount = () => {
      const cart = localStorage.getItem('cart');
      if (cart) {
        try {
          const items = JSON.parse(cart);
          setCartCount(items.reduce((sum: number, item: any) => sum + (item.quantity || 1), 0));
        } catch {
          setCartCount(0);
        }
      } else {
        setCartCount(0);
      }
    };
    updateCartCount();
    window.addEventListener('storage', updateCartCount);
    window.addEventListener('cartUpdated', updateCartCount);
    return () => {
      window.removeEventListener('storage', updateCartCount);
      window.removeEventListener('cartUpdated', updateCartCount);
    };
  }, []);

  // تبديل اللغة
  const switchLanguage = () => {
    const newLocale = getOppositeLocale(locale);
    let newPath = pathname.replace(`/${locale}`, `/${newLocale}`);

    // التأكد من أن المسار يبدأ بـ locale
    if (!newPath.startsWith(`/${newLocale}`)) {
      newPath = `/${newLocale}${newPath}`;
    }

    // حفظ تفضيل اللغة في localStorage والكوكيز
    localStorage.setItem('preferredLocale', newLocale);
    document.cookie = `preferredLocale=${newLocale}; path=/; max-age=31536000`; // سنة واحدة

    // الانتقال للصفحة الجديدة
    window.location.href = newPath;
  };

  // روابط التنقل من الإعدادات أو الافتراضية
  const navLinks = headerSettings?.navigationItems?.filter(item => item.isActive).map(item => ({
    key: item.nameEn.toLowerCase(),
    href: item.url.startsWith('/') ? `/${locale}${item.url === '/' ? '' : item.url}` : item.url,
    label: locale === 'ar' ? item.nameAr : item.nameEn
  })) || [
    { href: `/${locale}`, label: t('home'), key: 'home' },
    { href: `/${locale}/products`, label: t('products'), key: 'products' },
    { href: `/${locale}/about`, label: t('about'), key: 'about' },
    { href: `/${locale}/contact`, label: t('contact'), key: 'contact' },
  ];

  // اسم الموقع من الإعدادات
  const siteName = locale === 'ar' ? settings?.siteNameAr : settings?.siteName;
  const displaySiteName = siteName || 'DROOB HAJER';

  return (
    <header
      className="bg-gradient-to-r from-white to-gray-50 shadow-lg sticky top-0 z-50 border-b border-gray-100"
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 50,
        willChange: 'transform',
        backfaceVisibility: 'hidden'
      }}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <Link
              href={`/${locale}`}
              className="text-3xl font-['Pacifico'] text-primary hover:text-secondary transition-colors duration-300 transform hover:scale-105"
            >
              {displaySiteName}
            </Link>
            <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
              {/* قائمة الفئات */}
              <CategoriesDropdown locale={locale} />

              {navLinks.map((link) => (
                <Link
                  key={link.key}
                  href={link.href}
                  className={`relative font-semibold transition-all duration-300 group ${
                    pathname === link.href
                      ? 'text-primary'
                      : 'text-gray-700 hover:text-primary'
                  }`}
                >
                  {link.label}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
              ))}
            </nav>
          </div>
          
          <div className="flex items-center gap-6">
            {/* Language Switcher */}
            <button
              onClick={switchLanguage}
              className="flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-primary transition-colors duration-200 rounded-lg hover:bg-gray-100"
            >
              <span className="font-medium">
                {locale === 'ar' ? 'العربية' : 'English'}
              </span>
              <div className="w-4 h-4 flex items-center justify-center">
                <i className="ri-global-line"></i>
              </div>
            </button>

            {/* Cart */}
            <Link href={`/${locale}/cart`} className="relative group">
              <div className="w-12 h-12 flex items-center justify-center text-gray-700 hover:text-primary bg-gray-100 hover:bg-primary/10 rounded-full transition-all duration-300 transform group-hover:scale-110">
                <i className="ri-shopping-cart-2-line text-xl"></i>
              </div>
              {isMounted && cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 flex items-center justify-center rounded-full font-bold shadow-lg animate-pulse">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden w-12 h-12 flex items-center justify-center text-gray-700 hover:text-primary bg-gray-100 hover:bg-primary/10 rounded-full transition-all duration-300"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label={locale === 'ar' ? 'فتح القائمة الجانبية' : 'Open mobile menu'}
            >
              <i className={`ri-${mobileMenuOpen ? 'close' : 'menu'}-line text-xl transition-transform duration-300`}></i>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className={`md:hidden mt-4 transition-all duration-300 overflow-hidden ${mobileMenuOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0'}`}>
          <nav className="flex flex-col space-y-2 bg-gray-50 rounded-lg p-4">
            {navLinks.map((link) => (
              <Link 
                key={link.key}
                href={link.href} 
                className={`font-semibold py-3 px-4 rounded-lg transition-colors duration-200 ${
                  pathname === link.href
                    ? 'text-primary bg-white'
                    : 'text-gray-700 hover:bg-white hover:text-primary'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
