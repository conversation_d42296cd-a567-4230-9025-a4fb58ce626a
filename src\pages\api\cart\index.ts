import { NextApiRequest, NextApiResponse } from 'next';
import { getOrCreateSession } from '../../../lib/session-manager';
import { 
  getCart, 
  addToCart, 
  updateCartItemQuantity, 
  removeFromCart, 
  clearCart,
  migrateCartFromLocalStorage 
} from '../../../lib/cart-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // الحصول على معرف الجلسة أو إنشاء جلسة جديدة
    const sessionId = await getOrCreateSession(req, res);

    switch (req.method) {
      case 'GET':
        // جلب عربة التسوق
        const cart = await getCart(sessionId);
        return res.status(200).json({
          success: true,
          data: cart
        });

      case 'POST':
        // إضافة منتج إلى العربة
        const { product_id, product_title, product_title_ar, product_image, product_price, quantity } = req.body;

        if (!product_id || !product_title || !product_price || !quantity) {
          return res.status(400).json({
            success: false,
            message: 'بيانات المنتج غير مكتملة',
            messageEn: 'Product data is incomplete'
          });
        }

        const addedItem = await addToCart(sessionId, {
          product_id,
          product_title,
          product_title_ar,
          product_image,
          product_price: parseFloat(product_price),
          quantity: parseInt(quantity)
        });

        return res.status(200).json({
          success: true,
          message: 'تم إضافة المنتج إلى العربة بنجاح',
          messageEn: 'Product added to cart successfully',
          data: addedItem
        });

      case 'PUT':
        // تحديث كمية منتج
        const { product_id: updateProductId, quantity: newQuantity } = req.body;

        if (!updateProductId || newQuantity === undefined) {
          return res.status(400).json({
            success: false,
            message: 'معرف المنتج والكمية مطلوبان',
            messageEn: 'Product ID and quantity are required'
          });
        }

        const updated = await updateCartItemQuantity(sessionId, updateProductId, parseInt(newQuantity));

        if (!updated) {
          return res.status(404).json({
            success: false,
            message: 'المنتج غير موجود في العربة',
            messageEn: 'Product not found in cart'
          });
        }

        return res.status(200).json({
          success: true,
          message: 'تم تحديث الكمية بنجاح',
          messageEn: 'Quantity updated successfully'
        });

      case 'DELETE':
        const { product_id: deleteProductId, clear_all } = req.body;

        if (clear_all) {
          // تفريغ العربة بالكامل
          await clearCart(sessionId);
          return res.status(200).json({
            success: true,
            message: 'تم تفريغ العربة بنجاح',
            messageEn: 'Cart cleared successfully'
          });
        }

        if (!deleteProductId) {
          return res.status(400).json({
            success: false,
            message: 'معرف المنتج مطلوب',
            messageEn: 'Product ID is required'
          });
        }

        const removed = await removeFromCart(sessionId, deleteProductId);

        if (!removed) {
          return res.status(404).json({
            success: false,
            message: 'المنتج غير موجود في العربة',
            messageEn: 'Product not found in cart'
          });
        }

        return res.status(200).json({
          success: true,
          message: 'تم حذف المنتج من العربة بنجاح',
          messageEn: 'Product removed from cart successfully'
        });

      default:
        return res.status(405).json({
          success: false,
          message: 'طريقة الطلب غير مدعومة',
          messageEn: 'Method not allowed'
        });
    }

  } catch (error) {
    console.error('Cart API error:', error);
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي',
      messageEn: 'Internal server error'
    });
  }
}
