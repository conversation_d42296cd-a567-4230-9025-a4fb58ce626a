-- إن<PERSON>اء جدول الجلسات للمستخدمين
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 7 DAY),
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_session_active (is_active),
    INDEX idx_session_expires (expires_at)
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول عربة التسوق مرتبطة بالجلسات
CREATE TABLE IF NOT EXISTS cart_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(50) NOT NULL,
    product_title VARCHAR(255) NOT NULL,
    product_title_ar VARCHAR(255),
    product_image TEXT,
    product_price DECIMAL(10, 2) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session_product (session_id, product_id),
    INDEX idx_cart_session (session_id),
    INDEX idx_cart_product (product_id)
);

-- تحديث جدول طلبات الأسعار لربطها بالجلسات
ALTER TABLE quote_requests 
ADD COLUMN session_id VARCHAR(36) AFTER id,
ADD INDEX idx_quote_session (session_id);

-- إضافة foreign key للربط مع الجلسات (اختياري)
-- ALTER TABLE quote_requests 
-- ADD FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL;

-- إنشاء stored procedure لتنظيف الجلسات المنتهية
DELIMITER //
CREATE PROCEDURE CleanExpiredSessions()
BEGIN
    -- حذف عناصر عربة التسوق للجلسات المنتهية
    DELETE ci FROM cart_items ci
    INNER JOIN user_sessions us ON ci.session_id = us.id
    WHERE us.expires_at < NOW() OR us.is_active = FALSE;
    
    -- حذف الجلسات المنتهية
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() OR is_active = FALSE;
    
    SELECT ROW_COUNT() as deleted_sessions;
END //
DELIMITER ;

-- إنشاء event لتنظيف الجلسات تلقائياً كل ساعة
-- CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredSessions();
