import { NextApiRequest, NextApiResponse } from 'next';
import { getOrCreateSession } from '../../../lib/session-manager';
import { migrateCartFromLocalStorage, getCart } from '../../../lib/cart-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'طريقة الطلب غير مدعومة',
      messageEn: 'Method not allowed'
    });
  }

  try {
    // الحصول على معرف الجلسة أو إنشاء جلسة جديدة
    const sessionId = await getOrCreateSession(req, res);

    const { items } = req.body;

    if (!Array.isArray(items)) {
      return res.status(400).json({
        success: false,
        message: 'بيانات العربة غير صحيحة',
        messageEn: 'Invalid cart data'
      });
    }

    // ترحيل البيانات من localStorage
    const migrated = await migrateCartFromLocalStorage(sessionId, items);

    if (!migrated) {
      return res.status(500).json({
        success: false,
        message: 'فشل في ترحيل بيانات العربة',
        messageEn: 'Failed to migrate cart data'
      });
    }

    // جلب العربة المحدثة
    const cart = await getCart(sessionId);

    return res.status(200).json({
      success: true,
      message: 'تم ترحيل بيانات العربة بنجاح',
      messageEn: 'Cart data migrated successfully',
      data: cart
    });

  } catch (error) {
    console.error('Cart migration error:', error);
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم الداخلي',
      messageEn: 'Internal server error'
    });
  }
}
