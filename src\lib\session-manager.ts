import { v4 as uuidv4 } from 'uuid';
import { NextApiRequest, NextApiResponse } from 'next';
import { NextRequest, NextResponse } from 'next/server';
import { executeQuery, executeQuerySingle } from './database-config';

export interface UserSession {
  id: string;
  ip_address: string;
  user_agent: string;
  created_at: Date;
  updated_at: Date;
  expires_at: Date;
  is_active: boolean;
}

export interface CartItem {
  id: number;
  session_id: string;
  product_id: string;
  product_title: string;
  product_title_ar?: string;
  product_image?: string;
  product_price: number;
  quantity: number;
  created_at: Date;
  updated_at: Date;
}

// إنشاء جلسة جديدة
export async function createSession(ipAddress: string, userAgent: string): Promise<string> {
  const sessionId = uuidv4();
  
  try {
    await executeQuery(
      `INSERT INTO user_sessions (id, ip_address, user_agent, created_at, updated_at, expires_at, is_active) 
       VALUES (?, ?, ?, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), TRUE)`,
      [sessionId, ipAddress, userAgent]
    );
    
    console.log(`✅ تم إنشاء جلسة جديدة: ${sessionId}`);
    return sessionId;
  } catch (error) {
    console.error('خطأ في إنشاء الجلسة:', error);
    throw new Error('فشل في إنشاء الجلسة');
  }
}

// التحقق من صحة الجلسة
export async function validateSession(sessionId: string): Promise<UserSession | null> {
  if (!sessionId) return null;
  
  try {
    const session = await executeQuerySingle<UserSession>(
      `SELECT * FROM user_sessions 
       WHERE id = ? AND is_active = TRUE AND expires_at > NOW()`,
      [sessionId]
    );
    
    if (session) {
      // تحديث وقت آخر نشاط
      await executeQuery(
        'UPDATE user_sessions SET updated_at = NOW() WHERE id = ?',
        [sessionId]
      );
    }
    
    return session;
  } catch (error) {
    console.error('خطأ في التحقق من الجلسة:', error);
    return null;
  }
}

// تجديد انتهاء صلاحية الجلسة
export async function renewSession(sessionId: string): Promise<boolean> {
  try {
    const result = await executeQuery(
      `UPDATE user_sessions 
       SET expires_at = DATE_ADD(NOW(), INTERVAL 7 DAY), updated_at = NOW() 
       WHERE id = ? AND is_active = TRUE`,
      [sessionId]
    );
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('خطأ في تجديد الجلسة:', error);
    return false;
  }
}

// إنهاء الجلسة
export async function destroySession(sessionId: string): Promise<boolean> {
  try {
    const result = await executeQuery(
      'UPDATE user_sessions SET is_active = FALSE WHERE id = ?',
      [sessionId]
    );
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error('خطأ في إنهاء الجلسة:', error);
    return false;
  }
}

// تنظيف الجلسات المنتهية
export async function cleanExpiredSessions(): Promise<number> {
  try {
    const result = await executeQuery('CALL CleanExpiredSessions()');
    return result[0]?.[0]?.deleted_sessions || 0;
  } catch (error) {
    console.error('خطأ في تنظيف الجلسات:', error);
    return 0;
  }
}

// استخراج معرف الجلسة من الطلب (API Routes)
export function getSessionIdFromRequest(req: NextApiRequest): string | null {
  return req.cookies.sessionId || null;
}

// استخراج معرف الجلسة من الطلب (Middleware)
export function getSessionIdFromNextRequest(req: NextRequest): string | null {
  return req.cookies.get('sessionId')?.value || null;
}

// إعداد cookie الجلسة
export function setSessionCookie(res: NextApiResponse, sessionId: string): void {
  res.setHeader('Set-Cookie', [
    `sessionId=${sessionId}; Path=/; HttpOnly; SameSite=Strict; Max-Age=${7 * 24 * 60 * 60}` // 7 أيام
  ]);
}

// إعداد cookie الجلسة للـ middleware
export function setSessionCookieInResponse(response: NextResponse, sessionId: string): NextResponse {
  response.cookies.set('sessionId', sessionId, {
    httpOnly: true,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60, // 7 أيام
    path: '/'
  });
  
  return response;
}

// الحصول على IP address من الطلب
export function getClientIP(req: NextApiRequest | NextRequest): string {
  const forwarded = req.headers['x-forwarded-for'] as string;
  const realIP = req.headers['x-real-ip'] as string;
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  // في Next.js API routes
  if ('socket' in req && req.socket?.remoteAddress) {
    return req.socket.remoteAddress;
  }
  
  return '127.0.0.1'; // fallback
}

// الحصول على User Agent من الطلب
export function getUserAgent(req: NextApiRequest | NextRequest): string {
  return (req.headers['user-agent'] as string) || 'Unknown';
}

// إنشاء أو الحصول على جلسة من الطلب
export async function getOrCreateSession(req: NextApiRequest, res: NextApiResponse): Promise<string> {
  let sessionId = getSessionIdFromRequest(req);
  
  // التحقق من صحة الجلسة الموجودة
  if (sessionId) {
    const session = await validateSession(sessionId);
    if (session) {
      return sessionId;
    }
  }
  
  // إنشاء جلسة جديدة
  const ipAddress = getClientIP(req);
  const userAgent = getUserAgent(req);
  
  sessionId = await createSession(ipAddress, userAgent);
  setSessionCookie(res, sessionId);
  
  return sessionId;
}
